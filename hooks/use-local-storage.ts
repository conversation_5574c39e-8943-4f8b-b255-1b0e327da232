"use client"

import { useState, useEffect, type Dispatch, type SetStateAction } from "react"

function useLocalStorage<T>(key: string, initialValue: T): [T, Dispatch<SetStateAction<T>>] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue
    }
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.error("Error reading from localStorage", error)
      return initialValue
    }
  })

  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        window.localStorage.setItem(key, JSON.stringify(storedValue))
      } catch (error) {
        console.error("Error writing to localStorage", error)
      }
    }
  }, [key, storedValue])

  return [storedValue, setStoredValue]
}

export default useLocalStorage
