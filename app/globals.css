@import "tailwindcss";

@theme {
  --color-background: 0 0% 100%;
  --color-foreground: 0 0% 3.9%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 0 0% 3.9%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 0 0% 3.9%;
  --color-primary: 0 0% 9%;
  --color-primary-foreground: 0 0% 98%;
  --color-secondary: 0 0% 96.1%;
  --color-secondary-foreground: 0 0% 9%;
  --color-muted: 0 0% 96.1%;
  --color-muted-foreground: 0 0% 45.1%;
  --color-accent: 0 0% 96.1%;
  --color-accent-foreground: 0 0% 9%;
  --color-destructive: 0 84.2% 60.2%;
  --color-destructive-foreground: 0 0% 98%;
  --color-border: 0 0% 89.8%;
  --color-input: 0 0% 89.8%;
  --color-ring: 0 0% 3.9%;
  --radius: 0.5rem;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  * {
    border-color: hsl(var(--color-border));
  }
  body {
    background-color: hsl(var(--color-background));
    color: hsl(var(--color-foreground));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
