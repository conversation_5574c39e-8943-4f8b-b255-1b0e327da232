"use client"

import type React from "react"
import { useState, useMemo, useRef, useEffect } from "react"
import {
  DndContext,
  type DragEndEvent,
  DragOverlay,
  type DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  useDraggable,
  useDroppable,
} from "@dnd-kit/core"
import { arrayMove, SortableContext, useSortable, sortableKeyboardCoordinates } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { v4 as uuidv4 } from "uuid"
import html2canvas from "html2canvas"

import useLocalStorage from "@/hooks/use-local-storage"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip"
import {
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  Di<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import { Trash2, Upload, PlusCircle, Palette, X, Camera, Loader2, Copy, Pencil } from "lucide-react"

// --- TYPE DEFINITIONS ---
interface LibraryItem {
  id: string
  svgContent: string
  title: string
}

interface PlacedItem {
  instanceId: string
  libraryId: string
}

interface Tier {
  id: string
  title: string
  color: string
  items: PlacedItem[]
}

interface ActiveDragItem {
  type: "library" | "placed"
  libraryItem: LibraryItem
  instanceId?: string
}

// --- CONSTANTS ---
const defaultTiers: Tier[] = [
  { id: "S", title: "S", color: "bg-red-500", items: [] },
  { id: "A", title: "A", color: "bg-orange-500", items: [] },
  { id: "B", title: "B", color: "bg-amber-500", items: [] },
]
const tierColors = [
  "bg-red-500",
  "bg-orange-500",
  "bg-amber-500",
  "bg-yellow-500",
  "bg-lime-500",
  "bg-green-500",
  "bg-emerald-500",
  "bg-teal-500",
  "bg-cyan-500",
  "bg-sky-500",
  "bg-blue-500",
  "bg-indigo-500",
  "bg-violet-500",
  "bg-purple-500",
  "bg-fuchsia-500",
  "bg-pink-500",
  "bg-rose-500",
]

// --- REFACTORED & IMPROVED UI COMPONENTS ---

function SvgDisplay({ svgContent }: { svgContent: string }) {
  return (
    <div className="w-full h-full flex items-center justify-center" dangerouslySetInnerHTML={{ __html: svgContent }} />
  )
}

function TierItemCard({ item, libraryItem }: { item: PlacedItem; libraryItem: LibraryItem }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: item.instanceId,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div ref={setNodeRef} style={style} className="flex flex-col items-center gap-1">
      <div
        {...attributes}
        {...listeners}
        className="w-20 h-20 p-1 bg-muted rounded-md cursor-grab active:cursor-grabbing"
      >
        <SvgDisplay svgContent={libraryItem.svgContent} />
      </div>
      <p className="text-xs text-muted-foreground truncate w-20 text-center">{libraryItem.title}</p>
    </div>
  )
}

function LibraryItemCard({
  item,
  onRename,
  onDuplicate,
  onRemove,
}: {
  item: LibraryItem
  onRename: (id: string, newTitle: string) => void
  onDuplicate: (id: string) => void
  onRemove: (id: string) => void
}) {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: item.id,
    data: { type: "library", libraryId: item.id },
  })

  const handleRename = (e: React.MouseEvent) => {
    e.stopPropagation()
    const newTitle = prompt("Enter new name for this icon:", item.title)
    if (newTitle && newTitle.trim() !== "") {
      onRename(item.id, newTitle.trim())
    }
  }

  return (
    <div className="flex flex-col items-center gap-1">
      <div className="relative group">
        <div ref={setNodeRef} {...attributes} {...listeners} className="w-20 h-20 p-1 bg-muted rounded-md cursor-grab">
          <SvgDisplay svgContent={item.svgContent} />
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-black/70 to-transparent flex items-end justify-center gap-1 p-1 opacity-0 group-hover:opacity-100 transition-opacity rounded-b-md">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-white hover:bg-white/20"
                onClick={handleRename}
              >
                <Pencil className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Rename</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-white hover:bg-white/20"
                onClick={() => onDuplicate(item.id)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Duplicate</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                className="h-7 w-7 text-white hover:bg-white/20 hover:text-red-500"
                onClick={() => onRemove(item.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Remove</TooltipContent>
          </Tooltip>
        </div>
      </div>
      <p className="text-xs text-foreground truncate w-20 text-center">{item.title}</p>
    </div>
  )
}

function TrashCan() {
  const { setNodeRef, isOver } = useDroppable({ id: "trash-can" })
  return (
    <div
      ref={setNodeRef}
      className={`mt-8 p-4 border-2 border-dashed rounded-lg flex flex-col items-center justify-center transition-colors ${isOver ? "border-red-500 bg-red-500/10" : "border-muted-foreground"}`}
    >
      <Trash2 className={`h-10 w-10 transition-colors ${isOver ? "text-red-500" : "text-muted-foreground"}`} />
      <p className={`mt-2 font-semibold transition-colors ${isOver ? "text-red-500" : "text-muted-foreground"}`}>
        Drag here to remove
      </p>
    </div>
  )
}

// --- MAIN COMPONENT ---
export default function BuddyBench() {
  const [tiers, setTiers] = useLocalStorage<Tier[]>("buddybench-tiers-v2", defaultTiers)
  const [library, setLibrary] = useLocalStorage<LibraryItem[]>("buddybench-library-v2", [])
  const [activeDragItem, setActiveDragItem] = useState<ActiveDragItem | null>(null)
  const [isScreenshotModalOpen, setIsScreenshotModalOpen] = useState(false)
  const [screenshotTitle, setScreenshotTitle] = useState("My BuddyBench Tier List")
  const [screenshotParagraph, setScreenshotParagraph] = useState("Here's my definitive ranking!")
  const [isGenerating, setIsGenerating] = useState(false)
  const [isScreenshotting, setIsScreenshotting] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const tierListRef = useRef<HTMLDivElement>(null)

  const libraryItemsById = useMemo(
    () => library.reduce((acc, item) => ({ ...acc, [item.id]: item }), {} as Record<string, LibraryItem>),
    [library],
  )
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates }),
  )

  const wrapText = (
    ctx: CanvasRenderingContext2D,
    text: string,
    x: number,
    y: number,
    maxWidth: number,
    lineHeight: number,
  ): number => {
    const words = text.split(" ")
    let line = ""
    let testLine = ""
    let testWidth = 0
    let finalY = y
    let lineCount = 0

    for (let n = 0; n < words.length; n++) {
      testLine = line + words[n] + " "
      testWidth = ctx.measureText(testLine).width
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, finalY)
        line = words[n] + " "
        finalY += lineHeight
        lineCount++
      } else {
        line = testLine
      }
    }
    ctx.fillText(line, x, finalY)
    lineCount++
    return finalY + lineHeight * (lineCount - 1)
  }

  useEffect(() => {
    const handlePaste = (event: ClipboardEvent) => {
      const activeEl = document.activeElement
      if (activeEl && (activeEl.tagName === "INPUT" || activeEl.tagName === "TEXTAREA")) return
      const text = event.clipboardData?.getData("text/plain")
      if (text) {
        const trimmedText = text.trim()
        if (trimmedText.startsWith("<svg") && trimmedText.endsWith("</svg>")) {
          event.preventDefault()
          const title = prompt("Enter a title for the pasted SVG:", "Pasted SVG")
          if (title) {
            const newItem: LibraryItem = { id: uuidv4(), svgContent: trimmedText, title }
            setLibrary((prev) => [...prev, newItem])
            alert("SVG pasted successfully from clipboard!")
          }
        }
      }
    }
    document.addEventListener("paste", handlePaste)
    return () => document.removeEventListener("paste", handlePaste)
  }, [setLibrary])

  const handleRenameLibraryItem = (itemId: string, newTitle: string) => {
    setLibrary((prev) => prev.map((item) => (item.id === itemId ? { ...item, title: newTitle } : item)))
  }
  const handleDuplicateLibraryItem = (itemId: string) => {
    const itemToDuplicate = library.find((item) => item.id === itemId)
    if (!itemToDuplicate) return
    const newItem: LibraryItem = { ...itemToDuplicate, id: uuidv4(), title: `${itemToDuplicate.title} (Copy)` }
    setLibrary((prev) => [...prev, newItem])
  }
  const handleRemoveLibraryItem = (itemId: string) => {
    if (window.confirm("Remove this item from the library and all tiers? This cannot be undone.")) {
      setLibrary((prev) => prev.filter((item) => item.id !== itemId))
      setTiers((prev) =>
        prev.map((tier) => ({ ...tier, items: tier.items.filter((item) => item.libraryId !== itemId) })),
      )
    }
  }

  const addTier = () =>
    setTiers([
      ...tiers,
      { id: uuidv4(), title: "New Tier", color: tierColors[Math.floor(Math.random() * tierColors.length)], items: [] },
    ])
  const removeTier = (tierId: string) => {
    if (window.confirm("Are you sure?")) setTiers(tiers.filter((t) => t.id !== tierId))
  }
  const updateTierTitle = (tierId: string, newTitle: string) =>
    setTiers(tiers.map((t) => (t.id === tierId ? { ...t, title: newTitle } : t)))
  const updateTierColor = (tierId: string) => {
    const currentTier = tiers.find((t) => t.id === tierId)
    if (!currentTier) return
    const nextIndex = (tierColors.indexOf(currentTier.color) + 1) % tierColors.length
    setTiers(tiers.map((t) => (t.id === tierId ? { ...t, color: tierColors[nextIndex] } : t)))
  }

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return
    const svgFiles = Array.from(files).filter((file) => file.type === "image/svg+xml")
    if (svgFiles.length === 0) {
      alert("Please select SVG files only.")
      return
    }
    const newItemsPromises = svgFiles.map(
      (file) =>
        new Promise<LibraryItem>((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = (e) => {
            const title = prompt(`Enter a title for "${file.name}":`, file.name.replace(/\.svg$/, ""))
            if (title) resolve({ id: uuidv4(), svgContent: e.target?.result as string, title })
            else reject(new Error("Cancelled"))
          }
          reader.onerror = reject
          reader.readAsText(file)
        }),
    )
    Promise.all(newItemsPromises)
      .then((newItems) => setLibrary((p) => [...p, ...newItems]))
      .catch(console.error)
    if (fileInputRef.current) fileInputRef.current.value = ""
  }

  const handleGenerateScreenshot = async () => {
    if (!tierListRef.current) return
    setIsScreenshotting(true)
    setIsGenerating(true)
    setTimeout(async () => {
      try {
        const tierListCanvas = await html2canvas(tierListRef.current, {
          backgroundColor: null,
          scale: 2,
          useCORS: true,
        })
        const tempCtx = document.createElement("canvas").getContext("2d")
        if (!tempCtx) throw new Error("No context")
        const padding = 50,
          spacing = 25,
          titleSize = 48,
          pSize = 24,
          pLineHeight = 30
        tempCtx.font = `${pSize}px sans-serif`
        const finalY = wrapText(tempCtx, screenshotParagraph, 0, 0, tierListCanvas.width / 2 - padding * 2, pLineHeight)
        const textHeight = finalY + pLineHeight
        const totalWidth = tierListCanvas.width / 2 + padding * 2
        const totalHeight = padding + titleSize + spacing + textHeight + spacing + tierListCanvas.height / 2 + padding
        const finalCanvas = document.createElement("canvas")
        finalCanvas.width = totalWidth
        finalCanvas.height = totalHeight
        const ctx = finalCanvas.getContext("2d")
        if (!ctx) throw new Error("No context")
        ctx.fillStyle = "#111827"
        ctx.fillRect(0, 0, totalWidth, totalHeight)
        ctx.fillStyle = "#ffffff"
        ctx.font = `bold ${titleSize}px sans-serif`
        ctx.textAlign = "center"
        ctx.fillText(screenshotTitle, totalWidth / 2, padding + titleSize)
        ctx.font = `${pSize}px sans-serif`
        wrapText(
          ctx,
          screenshotParagraph,
          totalWidth / 2,
          padding + titleSize + spacing + pSize,
          totalWidth - padding * 2,
          pLineHeight,
        )
        ctx.drawImage(
          tierListCanvas,
          padding,
          padding + titleSize + spacing + textHeight + spacing,
          tierListCanvas.width / 2,
          tierListCanvas.height / 2,
        )
        const link = document.createElement("a")
        link.download = `${screenshotTitle.toLowerCase().replace(/\s/g, "-")}.png`
        link.href = finalCanvas.toDataURL("image/png")
        link.click()
      } catch (error) {
        console.error(error)
        alert("Failed to generate screenshot.")
      } finally {
        setIsGenerating(false)
        setIsScreenshotting(false)
        setIsScreenshotModalOpen(false)
      }
    }, 100)
  }

  const findTierForInstance = (instanceId: string) =>
    tiers.find((tier) => tier.items.some((item) => item.instanceId === instanceId))

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const { type, libraryId, instanceId } = active.data.current || {}
    if (type && libraryId && libraryItemsById[libraryId]) {
      setActiveDragItem({ type, libraryItem: libraryItemsById[libraryId], instanceId })
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveDragItem(null)
    const { active, over } = event

    if (!over) return

    const activeId = active.id as string
    const overId = over.id as string

    // Scenario 0: Dropping a placed item in the trash
    if (over.id === "trash-can" && active.data.current?.type === "placed") {
      const sourceTier = findTierForInstance(activeId)
      if (sourceTier) {
        setTiers((prevTiers) =>
          prevTiers.map((t) =>
            t.id === sourceTier.id ? { ...t, items: t.items.filter((i) => i.instanceId !== activeId) } : t,
          ),
        )
      }
      return
    }

    // Find the destination tier. This is the most reliable way.
    const overContainerId = over.data.current?.sortable?.containerId || over.id
    const destTier = tiers.find((t) => t.id === overContainerId)
    if (!destTier) return

    // Scenario 1: Dragging from the library into a tier
    if (active.data.current?.type === "library") {
      const newPlacedItem: PlacedItem = { instanceId: uuidv4(), libraryId: activeId }
      const overIndex = destTier.items.findIndex((i) => i.instanceId === overId)
      const insertionIndex = overIndex !== -1 ? overIndex : destTier.items.length

      setTiers((prevTiers) =>
        prevTiers.map((t) => {
          if (t.id === destTier.id) {
            const newItems = [...t.items]
            newItems.splice(insertionIndex, 0, newPlacedItem)
            return { ...t, items: newItems }
          }
          return t
        }),
      )
      return
    }

    // Scenario 2: Moving a placed item
    if (active.data.current?.type === "placed") {
      const sourceTier = findTierForInstance(activeId)
      if (!sourceTier) return

      // Sub-scenario 2a: Sorting within the same tier
      if (sourceTier.id === destTier.id) {
        if (activeId === overId) return // Dropped on itself
        const oldIndex = sourceTier.items.findIndex((i) => i.instanceId === activeId)
        const newIndex = destTier.items.findIndex((i) => i.instanceId === overId)
        if (oldIndex !== -1 && newIndex !== -1) {
          setTiers((prevTiers) =>
            prevTiers.map((t) =>
              t.id === sourceTier.id ? { ...t, items: arrayMove(t.items, oldIndex, newIndex) } : t,
            ),
          )
        }
      } else {
        // Sub-scenario 2b: Moving between different tiers
        const [movedItem] = sourceTier.items.filter((i) => i.instanceId === activeId)
        if (!movedItem) return

        const overIndex = destTier.items.findIndex((i) => i.instanceId === overId)
        const insertionIndex = overIndex !== -1 ? overIndex : destTier.items.length

        setTiers((prevTiers) =>
          prevTiers.map((t) => {
            // Remove from source tier
            if (t.id === sourceTier.id) {
              return { ...t, items: t.items.filter((i) => i.instanceId !== activeId) }
            }
            // Add to destination tier
            if (t.id === destTier.id) {
              const newItems = [...t.items]
              newItems.splice(insertionIndex, 0, movedItem)
              return { ...t, items: newItems }
            }
            return t
          }),
        )
      }
    }
  }

  return (
    <TooltipProvider>
      <style>{`.screenshot-mode .tier-controls, .screenshot-mode .item-title { display: none; } .screenshot-mode .tier-title-input { box-shadow: none !important; border-color: transparent !important; pointer-events: none; }`}</style>
      <div className={isScreenshotting ? "screenshot-mode" : ""}>
        <DndContext sensors={sensors} onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
          <div className="space-y-8">
            <Card>
              <CardContent className="p-4">
                <h2 className="text-2xl font-bold mb-4 text-center">Item Library</h2>
                <div className="flex flex-wrap justify-center gap-4 mb-2">
                  <Button onClick={() => fileInputRef.current?.click()}>
                    <Upload className="mr-2 h-4 w-4" /> Import SVGs
                  </Button>
                  <Dialog open={isScreenshotModalOpen} onOpenChange={setIsScreenshotModalOpen}>
                    <DialogTrigger asChild>
                      <Button variant="secondary">
                        <Camera className="mr-2 h-4 w-4" /> Screenshot
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create a Screenshot</DialogTitle>
                        <DialogDescription>Add a title and description to your tier list image.</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <Input
                          id="title"
                          value={screenshotTitle}
                          onChange={(e) => setScreenshotTitle(e.target.value)}
                          placeholder="Tier List Title"
                        />
                        <Textarea
                          id="paragraph"
                          value={screenshotParagraph}
                          onChange={(e) => setScreenshotParagraph(e.target.value)}
                          placeholder="A witty description..."
                        />
                      </div>
                      <DialogFooter>
                        <Button onClick={handleGenerateScreenshot} disabled={isGenerating}>
                          {isGenerating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null} Generate & Download
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  <Input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept="image/svg+xml"
                    multiple
                    onChange={handleFileImport}
                  />
                </div>
                <p className="text-center text-sm text-muted-foreground mb-4">
                  Or paste SVG code anywhere on the page.
                </p>
                {library.length > 0 ? (
                  <div className="grid grid-cols-[repeat(auto-fill,minmax(80px,1fr))] gap-x-4 gap-y-6 p-2 bg-muted/50 rounded-lg min-h-[116px]">
                    {library.map((item) => (
                      <LibraryItemCard
                        key={item.id}
                        item={item}
                        onRename={handleRenameLibraryItem}
                        onDuplicate={handleDuplicateLibraryItem}
                        onRemove={handleRemoveLibraryItem}
                      />
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground">Import some SVGs to get started!</p>
                )}
              </CardContent>
            </Card>

            <div ref={tierListRef} className="space-y-4 bg-gray-900 p-4 rounded-lg">
              {tiers.map((tier) => (
                <SortableContext key={tier.id} items={tier.items.map((i) => i.instanceId)} id={tier.id}>
                  <div className="flex items-stretch min-h-[140px]">
                    <div
                      className={`flex flex-col items-center justify-center w-24 text-white font-bold text-xl rounded-l-lg flex-shrink-0 p-2 ${tier.color}`}
                    >
                      <Input
                        value={tier.title}
                        onChange={(e) => updateTierTitle(tier.id, e.target.value)}
                        className="tier-title-input bg-transparent border-0 text-center text-white font-bold text-xl h-auto p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                      />
                      <div className="tier-controls flex gap-2 mt-2">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6 hover:bg-white/20"
                          onClick={() => updateTierColor(tier.id)}
                        >
                          <Palette className="h-4 w-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6 hover:bg-white/20"
                          onClick={() => removeTier(tier.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <Card className="flex-1 rounded-l-none rounded-r-lg bg-gray-800 border-gray-700">
                      <CardContent className="p-2 h-full">
                        <div className="grid grid-cols-[repeat(auto-fill,minmax(80px,1fr))] gap-x-4 gap-y-6">
                          {tier.items.map((item) => {
                            const libraryItem = libraryItemsById[item.libraryId]
                            if (!libraryItem) return null
                            return <TierItemCard key={item.instanceId} item={item} libraryItem={libraryItem} />
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </SortableContext>
              ))}
            </div>

            <div className="flex justify-center">
              <Button onClick={addTier}>
                <PlusCircle className="mr-2 h-4 w-4" /> Add Tier
              </Button>
            </div>
            <TrashCan />
          </div>
          <DragOverlay>
            {activeDragItem ? (
              <div className="w-20 h-20 p-1 bg-muted rounded-md flex items-center justify-center scale-110 shadow-lg">
                <SvgDisplay svgContent={activeDragItem.libraryItem.svgContent} />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>
    </TooltipProvider>
  )
}
